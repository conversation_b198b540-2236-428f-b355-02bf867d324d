/**
 * Meta Conversions API 事件跟踪工具
 * 为 CSR App 提供 Meta 事件上报功能
 */

import {
  createMetaReporter,
  MetaEventName,
  type MetaReportingService,
} from 'shared'
import { useUserStore } from '@/store/user'
import { getDynamicApiHost } from '@/utils/dynamicApiHost'

// 创建全局Meta事件上报器
const createGlobalMetaReporter = () => {
  const userStore = useUserStore()

  return createMetaReporter({
    apiBase: getDynamicApiHost(),
    authTokenGetter: () => userStore.token,
    userDataGetter: () => userStore.userInfo,
    debug: import.meta.env.DEV,
    retryConfig: {
      maxRetries: 3,
      retryDelay: 1500,
      backoffMultiplier: 2,
    },
    batchConfig: {
      maxBatchSize: 8,
      flushInterval: 4000,
    },
    defaultActionSource: 'website',
  })
}

// 全局上报器实例
export const metaReporter = createGlobalMetaReporter()

/**
 * Meta事件跟踪器类
 * 提供静态方法进行各种事件跟踪
 */
export class MetaEventTracker {
  /**
   * 跟踪购买事件
   */
  static async trackPurchase(params: {
    value: number
    currency?: string
    orderId?: string
    productId?: string
    productName?: string
    quantity?: number
  }) {
    try {
      return await metaReporter.reportEvent(MetaEventName.Purchase, {
        currency: params.currency || 'usd',
        value: params.value,
        order_id: params.orderId,
        contents: params.productId
          ? [
              {
                id: params.productId,
                quantity: params.quantity || 1,
                item_price: params.value,
                title: params.productName,
                delivery_category: 'home_delivery' as const,
              },
            ]
          : undefined,
      })
    } catch (error) {
      console.error('Failed to track purchase:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪注册完成事件
   */
  static async trackRegistration() {
    try {
      return await metaReporter.reportEvent(
        MetaEventName.CompleteRegistration,
        {
          currency: 'usd',
          value: 0,
        },
      )
    } catch (error) {
      console.error('Failed to track registration:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪故事查看事件
   */
  static async trackStoryView(params: {
    storyId: string
    storyTitle: string
    category?: string
    author?: string
  }) {
    try {
      return await metaReporter.reportEvent(MetaEventName.ViewContent, {
        content_type: 'story',
        content_ids: [params.storyId],
        content_name: params.storyTitle,
        content_category: params.category,
      })
    } catch (error) {
      console.error('Failed to track story view:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪搜索事件
   */
  static async trackSearch(searchString: string, category?: string) {
    try {
      return await metaReporter.reportEvent(MetaEventName.Search, {
        search_string: searchString,
        content_category: category,
      })
    } catch (error) {
      console.error('Failed to track search:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪添加到购物车事件
   */
  static async trackAddToCart(params: {
    contentId: string
    contentName: string
    value: number
    currency?: string
    category?: string
  }) {
    try {
      return await metaReporter.reportEvent(MetaEventName.AddToCart, {
        content_ids: [params.contentId],
        content_name: params.contentName,
        currency: params.currency || 'usd',
        value: params.value,
        content_category: params.category,
      })
    } catch (error) {
      console.error('Failed to track add to cart:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪发起结账事件
   */
  static async trackInitiateCheckout(params: {
    value: number
    currency?: string
    numItems?: number
    contents?: Array<{
      id: string
      quantity: number
      item_price?: number
    }>
  }) {
    try {
      return await metaReporter.reportEvent(MetaEventName.InitiateCheckout, {
        currency: params.currency || 'usd',
        value: params.value,
        num_items: params.numItems,
        contents: params.contents?.map((item) => ({
          id: item.id,
          quantity: item.quantity,
          item_price: item.item_price,
          delivery_category: 'home_delivery' as const,
        })),
      })
    } catch (error) {
      console.error('Failed to track initiate checkout:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪订阅事件
   */
  static async trackSubscribe(params: {
    value: number
    currency?: string
    subscriptionType?: string
    predictedLtv?: number
  }) {
    try {
      return await metaReporter.reportEvent(MetaEventName.Subscribe, {
        currency: params.currency || 'usd',
        value: params.value,
        content_name: params.subscriptionType,
        predicted_ltv: params.predictedLtv,
      })
    } catch (error) {
      console.error('Failed to track subscribe:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪潜在客户事件
   */
  static async trackLead(params: {
    value?: number
    currency?: string
    contentName?: string
  }) {
    try {
      return await metaReporter.reportEvent(MetaEventName.Lead, {
        currency: params.currency || 'usd',
        value: params.value || 0,
        content_name: params.contentName,
      })
    } catch (error) {
      console.error('Failed to track lead:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪添加支付信息事件
   */
  static async trackAddPaymentInfo(params: {
    value?: number
    currency?: string
    contentCategory?: string
  }) {
    try {
      return await metaReporter.reportEvent(MetaEventName.AddPaymentInfo, {
        currency: params.currency || 'usd',
        value: params.value || 0,
        content_category: params.contentCategory,
      })
    } catch (error) {
      console.error('Failed to track add payment info:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 批量跟踪事件
   */
  static async trackEvents(
    events: Array<{
      eventName: MetaEventName | string
      customData?: any
      options?: {
        actionSource?: 'website' | 'app' | 'email'
        eventSourceUrl?: string
        eventId?: string
      }
    }>,
  ) {
    try {
      return await metaReporter.reportEvents(events)
    } catch (error) {
      console.error('Failed to track batch events:', error)
      return events.map(() => ({ success: false, error: error.message }))
    }
  }

  /**
   * 获取队列状态
   */
  static getQueueStatus() {
    return metaReporter.getQueueStatus()
  }

  /**
   * 立即刷新队列
   */
  static async flushQueue() {
    try {
      await metaReporter.flushQueue()
    } catch (error) {
      console.error('Failed to flush queue:', error)
    }
  }

  /**
   * 清空队列
   */
  static clearQueue() {
    metaReporter.clearQueue()
  }
}

// 导出便捷函数
export const trackPurchase = MetaEventTracker.trackPurchase
export const trackRegistration = MetaEventTracker.trackRegistration
export const trackStoryView = MetaEventTracker.trackStoryView
export const trackSearch = MetaEventTracker.trackSearch
export const trackAddToCart = MetaEventTracker.trackAddToCart
export const trackInitiateCheckout = MetaEventTracker.trackInitiateCheckout
export const trackSubscribe = MetaEventTracker.trackSubscribe
export const trackLead = MetaEventTracker.trackLead
export const trackAddPaymentInfo = MetaEventTracker.trackAddPaymentInfo
export const trackEvents = MetaEventTracker.trackEvents

/**
 * 与现有事件系统集成
 */
import { reportEvent as originalReportEvent, ReportEvent } from '@/utils/report'

// 事件映射表 - 将现有事件类型映射到Meta事件
const eventMapping: Record<
  ReportEvent,
  { metaEvent: MetaEventName; dataMapper?: (data: any) => any }
> = {
  [ReportEvent.PaymentSuccess]: {
    metaEvent: MetaEventName.Purchase,
    dataMapper: (data) => ({
      currency: data.currency || 'usd',
      value: data.amount || data.value || 0,
      order_id: data.orderId || data.order_id,
    }),
  },
  [ReportEvent.UserRegister]: {
    metaEvent: MetaEventName.CompleteRegistration,
    dataMapper: () => ({
      currency: 'usd',
      value: 0,
    }),
  },
  // 可以根据需要添加更多映射
}

/**
 * 增强的事件上报函数
 * 同时上报到现有系统和Meta Conversions API
 */
export async function reportEventWithMeta(
  eventType: ReportEvent,
  eventData: object = {},
  metaCustomData?: any,
) {
  // 上报到现有系统
  const existingResult = originalReportEvent(eventType, eventData)

  // 检查是否有对应的Meta事件映射
  const mapping = eventMapping[eventType]
  if (mapping) {
    try {
      const customData =
        metaCustomData ||
        (mapping.dataMapper ? mapping.dataMapper(eventData) : {})
      await metaReporter.reportEvent(mapping.metaEvent, customData)
    } catch (error) {
      console.warn('Meta event reporting failed:', error)
    }
  }

  return existingResult
}

/**
 * 专门用于支付成功的Meta事件上报
 */
export async function reportPaymentSuccessWithMeta(paymentData: {
  amount: number
  currency?: string
  orderId?: string
  productId?: string
  productName?: string
}) {
  // 上报到现有系统
  originalReportEvent(ReportEvent.PaymentSuccess, paymentData)

  // 上报到Meta
  return await MetaEventTracker.trackPurchase({
    value: paymentData.amount,
    currency: paymentData.currency,
    orderId: paymentData.orderId,
    productId: paymentData.productId,
    productName: paymentData.productName,
  })
}
