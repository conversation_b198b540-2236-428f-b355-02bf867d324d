<!--
  CSR App 中 Meta 事件跟踪的使用示例
  展示如何在 Vue 组件中使用 MetaEventTracker
-->

<template>
  <div class="meta-tracking-examples">
    <h1>Meta 事件跟踪示例 (CSR App)</h1>
    
    <!-- 故事查看示例 -->
    <section class="story-section">
      <h2>故事查看跟踪</h2>
      <div v-if="story" class="story-card">
        <h3>{{ story.title }}</h3>
        <p>{{ story.description }}</p>
        <button @click="viewStory">查看故事</button>
      </div>
    </section>

    <!-- 搜索示例 -->
    <section class="search-section">
      <h2>搜索跟踪</h2>
      <input 
        v-model="searchQuery" 
        @keyup.enter="performSearch"
        placeholder="搜索故事..."
      />
      <button @click="performSearch">搜索</button>
    </section>

    <!-- 支付流程示例 -->
    <section class="payment-section">
      <h2>支付流程跟踪</h2>
      <div class="payment-flow">
        <div class="step">
          <h4>1. 添加到购物车</h4>
          <button @click="addToCart">添加商品</button>
        </div>
        <div class="step">
          <h4>2. 发起结账</h4>
          <button @click="initiateCheckout">开始结账</button>
        </div>
        <div class="step">
          <h4>3. 添加支付信息</h4>
          <button @click="addPaymentInfo">添加支付方式</button>
        </div>
        <div class="step">
          <h4>4. 完成购买</h4>
          <button @click="completePurchase">确认支付</button>
        </div>
      </div>
    </section>

    <!-- 用户行为示例 -->
    <section class="user-actions">
      <h2>用户行为跟踪</h2>
      <div class="actions">
        <button @click="trackRegistration">模拟注册</button>
        <button @click="trackLead">潜在客户</button>
        <button @click="trackSubscribe">订阅服务</button>
      </div>
    </section>

    <!-- 批量事件示例 -->
    <section class="batch-events">
      <h2>批量事件跟踪</h2>
      <button @click="trackBatchEvents">发送批量事件</button>
    </section>

    <!-- 增强事件上报示例 -->
    <section class="enhanced-reporting">
      <h2>增强事件上报 (集成现有系统)</h2>
      <button @click="reportWithMeta">使用增强上报</button>
      <button @click="reportPaymentWithMeta">支付成功上报</button>
    </section>

    <!-- 队列管理 -->
    <section class="queue-management">
      <h2>队列管理</h2>
      <div class="queue-info">
        <p>队列长度: {{ queueStatus.queueLength }}</p>
        <p>正在处理: {{ queueStatus.isProcessing ? '是' : '否' }}</p>
        <p>待重试: {{ queueStatus.pendingRetries }}</p>
      </div>
      <div class="queue-actions">
        <button @click="refreshStatus">刷新状态</button>
        <button @click="flushQueue">立即发送</button>
        <button @click="clearQueue">清空队列</button>
      </div>
    </section>

    <!-- 事件日志 -->
    <section class="event-log">
      <h2>事件日志</h2>
      <div class="log-container">
        <div 
          v-for="(log, index) in eventLogs" 
          :key="index"
          :class="['log-entry', log.success ? 'success' : 'error']"
        >
          <span class="timestamp">{{ log.timestamp }}</span>
          <span class="event-type">{{ log.eventType }}</span>
          <span class="status">{{ log.success ? '✅' : '❌' }}</span>
          <span class="message">{{ log.message }}</span>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { 
  MetaEventTracker,
  reportEventWithMeta,
  reportPaymentSuccessWithMeta
} from '@/utils/metaTracking'
import { ReportEvent } from '@/utils/report'

export default {
  name: 'MetaTrackingUsage',
  
  data() {
    return {
      searchQuery: '',
      story: {
        id: 'story-romance-001',
        title: 'Romantic Adventure',
        description: 'A beautiful love story...',
        category: 'romance'
      },
      queueStatus: {
        queueLength: 0,
        isProcessing: false,
        pendingRetries: 0
      },
      eventLogs: []
    }
  },

  mounted() {
    this.refreshStatus()
    
    // 定期刷新队列状态
    this.statusInterval = setInterval(() => {
      this.refreshStatus()
    }, 3000)
  },

  beforeUnmount() {
    if (this.statusInterval) {
      clearInterval(this.statusInterval)
    }
  },

  methods: {
    // 添加日志
    addLog(eventType, success, message) {
      this.eventLogs.unshift({
        timestamp: new Date().toLocaleTimeString(),
        eventType,
        success,
        message
      })
      
      // 只保留最近20条日志
      if (this.eventLogs.length > 20) {
        this.eventLogs = this.eventLogs.slice(0, 20)
      }
    },

    // 故事查看
    async viewStory() {
      try {
        const result = await MetaEventTracker.trackStoryView({
          storyId: this.story.id,
          storyTitle: this.story.title,
          category: this.story.category
        })
        
        this.addLog('ViewContent', result.success, 
          result.success ? '故事查看事件已上报' : result.error)
      } catch (error) {
        this.addLog('ViewContent', false, error.message)
      }
    },

    // 搜索
    async performSearch() {
      if (!this.searchQuery.trim()) return
      
      try {
        const result = await MetaEventTracker.trackSearch(this.searchQuery, 'romance')
        this.addLog('Search', result.success, 
          result.success ? `搜索: ${this.searchQuery}` : result.error)
      } catch (error) {
        this.addLog('Search', false, error.message)
      }
    },

    // 添加到购物车
    async addToCart() {
      try {
        const result = await MetaEventTracker.trackAddToCart({
          contentId: 'premium-story-pack',
          contentName: 'Premium Story Pack',
          value: 29.99,
          currency: 'usd'
        })
        
        this.addLog('AddToCart', result.success, 
          result.success ? '添加到购物车' : result.error)
      } catch (error) {
        this.addLog('AddToCart', false, error.message)
      }
    },

    // 发起结账
    async initiateCheckout() {
      try {
        const result = await MetaEventTracker.trackInitiateCheckout({
          value: 29.99,
          currency: 'usd',
          numItems: 1
        })
        
        this.addLog('InitiateCheckout', result.success, 
          result.success ? '发起结账' : result.error)
      } catch (error) {
        this.addLog('InitiateCheckout', false, error.message)
      }
    },

    // 添加支付信息
    async addPaymentInfo() {
      try {
        const result = await MetaEventTracker.trackAddPaymentInfo({
          value: 29.99,
          currency: 'usd'
        })
        
        this.addLog('AddPaymentInfo', result.success, 
          result.success ? '添加支付信息' : result.error)
      } catch (error) {
        this.addLog('AddPaymentInfo', false, error.message)
      }
    },

    // 完成购买
    async completePurchase() {
      try {
        const orderId = `order-${Date.now()}`
        const result = await MetaEventTracker.trackPurchase({
          value: 29.99,
          currency: 'usd',
          orderId,
          productId: 'premium-story-pack'
        })
        
        this.addLog('Purchase', result.success, 
          result.success ? `购买完成: ${orderId}` : result.error)
      } catch (error) {
        this.addLog('Purchase', false, error.message)
      }
    },

    // 注册
    async trackRegistration() {
      try {
        const result = await MetaEventTracker.trackRegistration()
        this.addLog('CompleteRegistration', result.success, 
          result.success ? '用户注册' : result.error)
      } catch (error) {
        this.addLog('CompleteRegistration', false, error.message)
      }
    },

    // 潜在客户
    async trackLead() {
      try {
        const result = await MetaEventTracker.trackLead({
          contentName: 'Newsletter Signup'
        })
        this.addLog('Lead', result.success, 
          result.success ? '潜在客户' : result.error)
      } catch (error) {
        this.addLog('Lead', false, error.message)
      }
    },

    // 订阅
    async trackSubscribe() {
      try {
        const result = await MetaEventTracker.trackSubscribe({
          value: 19.99,
          subscriptionType: 'Monthly Premium'
        })
        this.addLog('Subscribe', result.success, 
          result.success ? '订阅服务' : result.error)
      } catch (error) {
        this.addLog('Subscribe', false, error.message)
      }
    },

    // 批量事件
    async trackBatchEvents() {
      try {
        const events = [
          {
            eventName: 'ViewContent',
            customData: { content_type: 'story', content_ids: ['story-1'] }
          },
          {
            eventName: 'ViewContent', 
            customData: { content_type: 'story', content_ids: ['story-2'] }
          },
          {
            eventName: 'Search',
            customData: { search_string: 'romance' }
          }
        ]
        
        const results = await MetaEventTracker.trackEvents(events)
        const successCount = results.filter(r => r.success).length
        
        this.addLog('BatchEvents', successCount === events.length, 
          `批量事件: ${successCount}/${events.length} 成功`)
      } catch (error) {
        this.addLog('BatchEvents', false, error.message)
      }
    },

    // 增强事件上报
    async reportWithMeta() {
      try {
        await reportEventWithMeta(ReportEvent.UserRegister, {
          userId: 'user-123'
        })
        this.addLog('EnhancedReport', true, '增强事件上报 (注册)')
      } catch (error) {
        this.addLog('EnhancedReport', false, error.message)
      }
    },

    // 支付成功增强上报
    async reportPaymentWithMeta() {
      try {
        await reportPaymentSuccessWithMeta({
          amount: 29.99,
          currency: 'usd',
          orderId: `order-${Date.now()}`,
          productId: 'premium-pack'
        })
        this.addLog('PaymentWithMeta', true, '支付成功增强上报')
      } catch (error) {
        this.addLog('PaymentWithMeta', false, error.message)
      }
    },

    // 刷新状态
    refreshStatus() {
      this.queueStatus = MetaEventTracker.getQueueStatus()
    },

    // 立即发送
    async flushQueue() {
      try {
        await MetaEventTracker.flushQueue()
        this.addLog('FlushQueue', true, '队列已刷新')
        this.refreshStatus()
      } catch (error) {
        this.addLog('FlushQueue', false, error.message)
      }
    },

    // 清空队列
    clearQueue() {
      MetaEventTracker.clearQueue()
      this.addLog('ClearQueue', true, '队列已清空')
      this.refreshStatus()
    }
  }
}
</script>

<style scoped>
.meta-tracking-examples {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #fff;
}

h1, h2 {
  color: #333;
  margin-bottom: 15px;
}

.story-card {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin: 10px 0;
}

.payment-flow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.step {
  background: #e9ecef;
  padding: 15px;
  border-radius: 5px;
  text-align: center;
}

.actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.queue-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 15px;
}

.queue-info p {
  margin: 5px 0;
  font-family: monospace;
}

.queue-actions {
  display: flex;
  gap: 10px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 5px;
  padding: 10px;
}

.log-entry {
  display: grid;
  grid-template-columns: 80px 120px 30px 1fr;
  gap: 10px;
  padding: 5px;
  border-bottom: 1px solid #dee2e6;
  font-family: monospace;
  font-size: 12px;
}

.log-entry.success {
  color: #28a745;
}

.log-entry.error {
  color: #dc3545;
}

button {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin: 2px;
}

button:hover {
  background: #0056b3;
}

input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 10px;
  width: 200px;
}
</style>
