<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meta API Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .section {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        textarea {
            height: 100px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Meta Conversions API Debug Tool</h1>
    
    <div class="container">
        <!-- 工作示例测试 -->
        <div class="section">
            <h2>工作示例测试</h2>
            <p>使用你提供的完全相同的代码</p>
            
            <label>API URL:</label>
            <input type="text" id="workingApiUrl" value="https://api-test.zhijianyuzhou.com/api/v1/meta-api.forward">
            
            <label>认证Token (可选):</label>
            <input type="text" id="workingToken" placeholder="Bearer token">
            
            <button onclick="testWorkingExample()">测试工作示例</button>
            
            <div id="workingLog" class="log"></div>
        </div>

        <!-- 我们的实现测试 -->
        <div class="section">
            <h2>我们的实现测试</h2>
            <p>使用shared包的MetaApiClient</p>
            
            <label>API URL:</label>
            <input type="text" id="ourApiUrl" value="https://api-test.zhijianyuzhou.com">
            
            <label>认证Token (可选):</label>
            <input type="text" id="ourToken" placeholder="Bearer token">
            
            <button onclick="testOurImplementation()">测试我们的实现</button>
            <button onclick="testWithWorkingExample()">测试完全一致的请求</button>
            
            <div id="ourLog" class="log"></div>
        </div>
    </div>

    <!-- 请求对比 -->
    <div class="section" style="margin-top: 20px;">
        <h2>请求对比</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h3>工作示例请求</h3>
                <textarea id="workingRequest" readonly></textarea>
            </div>
            <div>
                <h3>我们的请求</h3>
                <textarea id="ourRequest" readonly></textarea>
            </div>
        </div>
    </div>

    <script type="module">
        // 导入我们的shared包 (需要在实际项目中使用)
        // import { createMetaApiClient } from '../dist/index.mjs'

        let workingRequestDetails = '';
        let ourRequestDetails = '';

        // 工作示例测试
        window.testWorkingExample = async function() {
            const log = document.getElementById('workingLog');
            const apiUrl = document.getElementById('workingApiUrl').value;
            const token = document.getElementById('workingToken').value;
            
            log.innerHTML = '开始测试工作示例...\n';
            
            try {
                // 要发送的事件数据
                const eventData = [
                    {
                        event_name: "Purchase",
                        event_time: Math.floor(Date.now() / 1000), // 使用当前时间
                        user_data: {
                            em: [
                                "309a0a5c3e211326ae75ca18196d301a9bdbd1a882a4d2569511033da23f0abd"
                            ],
                            ph: [
                                "254aa248acb47dd654ca3ea53f48c2c26d641d23d7e2e93a1ec56258df7674c4",
                                "6f4fcb9deaeadc8f9746ae76d97ce1239e98b404efe5da3ee0b7149740f89ad6"
                            ],
                            client_ip_address: "***************",
                            client_user_agent: navigator.userAgent,
                            fbc: "fb.1.1554763741205.AbCdEfGhIjKlMnOpQrStUvWxYz1234567890",
                            fbp: "fb.1.1558571054389.1098115397"
                        },
                        custom_data: {
                            currency: "usd",
                            value: 123.45,
                            contents: [
                                {
                                    id: "product123",
                                    quantity: 1,
                                    delivery_category: "home_delivery"
                                }
                            ]
                        },
                        event_source_url: window.location.href,
                        action_source: "website"
                    }
                ];

                // 创建 FormData
                const formData = new FormData();
                formData.append('data', JSON.stringify(eventData));

                // 准备请求选项
                const fetchOptions = {
                    method: 'POST',
                    body: formData
                };

                // 添加认证头（如果有）
                if (token) {
                    fetchOptions.headers = {
                        'Authorization': `Bearer ${token}`
                    };
                }

                // 记录请求详情
                workingRequestDetails = `URL: ${apiUrl}
Method: POST
Headers: ${JSON.stringify(fetchOptions.headers || {}, null, 2)}
FormData:
  data: ${JSON.stringify(eventData, null, 2)}`;
                
                document.getElementById('workingRequest').value = workingRequestDetails;

                log.innerHTML += '发送请求...\n';
                log.innerHTML += `URL: ${apiUrl}\n`;
                log.innerHTML += `Headers: ${JSON.stringify(fetchOptions.headers || {}, null, 2)}\n`;
                log.innerHTML += `FormData data: ${JSON.stringify(eventData, null, 2)}\n\n`;

                // 发送请求
                const response = await fetch(apiUrl, fetchOptions);
                
                log.innerHTML += `响应状态: ${response.status} ${response.statusText}\n`;
                
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = responseText;
                }
                
                if (response.ok) {
                    log.innerHTML += `<span class="success">✅ 成功!</span>\n`;
                    log.innerHTML += `响应: ${JSON.stringify(responseData, null, 2)}\n`;
                } else {
                    log.innerHTML += `<span class="error">❌ 失败!</span>\n`;
                    log.innerHTML += `响应: ${JSON.stringify(responseData, null, 2)}\n`;
                }
                
            } catch (error) {
                log.innerHTML += `<span class="error">❌ 错误: ${error.message}</span>\n`;
                console.error('Working example error:', error);
            }
        };

        // 模拟我们的实现测试（简化版）
        window.testOurImplementation = async function() {
            const log = document.getElementById('ourLog');
            const apiUrl = document.getElementById('ourApiUrl').value;
            const token = document.getElementById('ourToken').value;
            
            log.innerHTML = '开始测试我们的实现...\n';
            
            try {
                // 模拟我们的事件数据结构
                const events = [
                    {
                        event_name: "Purchase",
                        event_time: Math.floor(Date.now() / 1000),
                        user_data: {
                            em: [
                                "309a0a5c3e211326ae75ca18196d301a9bdbd1a882a4d2569511033da23f0abd"
                            ],
                            ph: [
                                "254aa248acb47dd654ca3ea53f48c2c26d641d23d7e2e93a1ec56258df7674c4",
                                "6f4fcb9deaeadc8f9746ae76d97ce1239e98b404efe5da3ee0b7149740f89ad6"
                            ],
                            client_ip_address: "***************",
                            client_user_agent: navigator.userAgent,
                            fbc: "fb.1.1554763741205.AbCdEfGhIjKlMnOpQrStUvWxYz1234567890",
                            fbp: "fb.1.1558571054389.1098115397"
                        },
                        custom_data: {
                            currency: "usd",
                            value: 123.45,
                            contents: [
                                {
                                    id: "product123",
                                    quantity: 1,
                                    delivery_category: "home_delivery"
                                }
                            ]
                        },
                        event_source_url: window.location.href,
                        action_source: "website"
                    }
                ];

                const url = `${apiUrl}/api/v1/meta-api.forward`;
                
                // 准备请求头
                const headers = {};
                if (token) {
                    headers.Authorization = `Bearer ${token}`;
                }

                // 准备FormData请求体
                const formData = new FormData();
                formData.append('data', JSON.stringify(events));

                // 记录请求详情
                ourRequestDetails = `URL: ${url}
Method: POST
Headers: ${JSON.stringify(headers, null, 2)}
FormData:
  data: ${JSON.stringify(events, null, 2)}`;
                
                document.getElementById('ourRequest').value = ourRequestDetails;

                log.innerHTML += '发送请求...\n';
                log.innerHTML += `URL: ${url}\n`;
                log.innerHTML += `Headers: ${JSON.stringify(headers, null, 2)}\n`;
                log.innerHTML += `FormData data: ${JSON.stringify(events, null, 2)}\n\n`;

                const response = await fetch(url, {
                    method: 'POST',
                    headers,
                    body: formData
                });

                log.innerHTML += `响应状态: ${response.status} ${response.statusText}\n`;
                
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = responseText;
                }
                
                if (response.ok) {
                    log.innerHTML += `<span class="success">✅ 成功!</span>\n`;
                    log.innerHTML += `响应: ${JSON.stringify(responseData, null, 2)}\n`;
                } else {
                    log.innerHTML += `<span class="error">❌ 失败!</span>\n`;
                    log.innerHTML += `响应: ${JSON.stringify(responseData, null, 2)}\n`;
                }
                
            } catch (error) {
                log.innerHTML += `<span class="error">❌ 错误: ${error.message}</span>\n`;
                console.error('Our implementation error:', error);
            }
        };

        // 测试完全一致的请求
        window.testWithWorkingExample = function() {
            // 这个函数与testOurImplementation相同，用于确保完全一致
            testOurImplementation();
        };
    </script>
</body>
</html>
