/**
 * CSR App 中的 Meta 事件上报集成示例
 */

import { createMetaReporter, MetaEventName } from '../src'
import { useUserStore } from '@/store/user'
import { getDynamicApiHost } from '@/utils/dynamicApiHost'

// 1. 创建全局Meta事件上报器
const userStore = useUserStore()

export const metaReporter = createMetaReporter({
  apiBase: getDynamicApiHost(),
  authTokenGetter: () => userStore.token,
  userDataGetter: () => userStore.userInfo,
  debug: import.meta.env.DEV,
  retryConfig: {
    maxRetries: 3,
    retryDelay: 1500,
    backoffMultiplier: 2
  },
  batchConfig: {
    maxBatchSize: 8,
    flushInterval: 4000
  }
})

// 2. 创建事件跟踪工具类
export class MetaEventTracker {
  /**
   * 跟踪购买事件
   */
  static async trackPurchase(params: {
    value: number
    currency?: string
    orderId?: string
    productId?: string
    quantity?: number
  }) {
    try {
      return await metaReporter.reportEvent(MetaEventName.Purchase, {
        currency: params.currency || 'usd',
        value: params.value,
        order_id: params.orderId,
        contents: params.productId ? [
          {
            id: params.productId,
            quantity: params.quantity || 1,
            item_price: params.value,
            delivery_category: 'home_delivery' as const
          }
        ] : undefined
      })
    } catch (error) {
      console.error('Failed to track purchase:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪注册完成事件
   */
  static async trackRegistration() {
    try {
      return await metaReporter.reportEvent(MetaEventName.CompleteRegistration, {
        currency: 'usd',
        value: 0
      })
    } catch (error) {
      console.error('Failed to track registration:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪故事查看事件
   */
  static async trackStoryView(params: {
    storyId: string
    storyTitle: string
    category?: string
  }) {
    try {
      return await metaReporter.reportEvent(MetaEventName.ViewContent, {
        content_type: 'story',
        content_ids: [params.storyId],
        content_name: params.storyTitle,
        content_category: params.category
      })
    } catch (error) {
      console.error('Failed to track story view:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪搜索事件
   */
  static async trackSearch(searchString: string) {
    try {
      return await metaReporter.reportEvent(MetaEventName.Search, {
        search_string: searchString
      })
    } catch (error) {
      console.error('Failed to track search:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪添加到购物车事件
   */
  static async trackAddToCart(params: {
    contentId: string
    contentName: string
    value: number
    currency?: string
  }) {
    try {
      return await metaReporter.reportEvent(MetaEventName.AddToCart, {
        content_ids: [params.contentId],
        content_name: params.contentName,
        currency: params.currency || 'usd',
        value: params.value
      })
    } catch (error) {
      console.error('Failed to track add to cart:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪发起结账事件
   */
  static async trackInitiateCheckout(params: {
    value: number
    currency?: string
    numItems?: number
  }) {
    try {
      return await metaReporter.reportEvent(MetaEventName.InitiateCheckout, {
        currency: params.currency || 'usd',
        value: params.value,
        num_items: params.numItems
      })
    } catch (error) {
      console.error('Failed to track initiate checkout:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪潜在客户事件
   */
  static async trackLead(params: {
    value?: number
    currency?: string
    contentName?: string
  }) {
    try {
      return await metaReporter.reportEvent(MetaEventName.Lead, {
        currency: params.currency || 'usd',
        value: params.value || 0,
        content_name: params.contentName
      })
    } catch (error) {
      console.error('Failed to track lead:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取队列状态
   */
  static getQueueStatus() {
    return metaReporter.getQueueStatus()
  }
}

// 3. 与现有事件上报系统集成
import { reportEvent, ReportEvent } from '@/utils/report'

/**
 * 增强的事件上报函数，同时上报到现有系统和Meta
 */
export async function reportEventWithMeta(
  eventType: ReportEvent,
  eventData: object = {},
  metaEventName?: MetaEventName,
  metaCustomData?: any
) {
  // 上报到现有系统
  const existingResult = reportEvent(eventType, eventData)

  // 如果指定了Meta事件，也上报到Meta
  if (metaEventName) {
    try {
      await metaReporter.reportEvent(metaEventName, metaCustomData)
    } catch (error) {
      console.warn('Meta event reporting failed:', error)
    }
  }

  return existingResult
}

// 4. 在组件中使用示例
/*
// 在故事详情组件中
import { MetaEventTracker } from '@/utils/metaTracking'

export default {
  async mounted() {
    // 跟踪故事查看
    await MetaEventTracker.trackStoryView({
      storyId: this.story.id,
      storyTitle: this.story.title,
      category: this.story.category
    })
  }
}
*/

/*
// 在支付组件中
import { MetaEventTracker } from '@/utils/metaTracking'

export default {
  methods: {
    async onPaymentSuccess(paymentData) {
      // 跟踪购买事件
      await MetaEventTracker.trackPurchase({
        value: paymentData.amount,
        currency: paymentData.currency,
        orderId: paymentData.orderId,
        productId: paymentData.productId
      })
    },
    
    async onInitiateCheckout(checkoutData) {
      // 跟踪发起结账事件
      await MetaEventTracker.trackInitiateCheckout({
        value: checkoutData.totalAmount,
        currency: checkoutData.currency,
        numItems: checkoutData.items.length
      })
    }
  }
}
*/

/*
// 在注册组件中
import { MetaEventTracker } from '@/utils/metaTracking'

export default {
  methods: {
    async onRegistrationComplete() {
      // 跟踪注册完成事件
      await MetaEventTracker.trackRegistration()
    }
  }
}
*/

/*
// 在搜索组件中
import { MetaEventTracker } from '@/utils/metaTracking'

export default {
  methods: {
    async onSearch(searchQuery) {
      // 跟踪搜索事件
      await MetaEventTracker.trackSearch(searchQuery)
    }
  }
}
*/
