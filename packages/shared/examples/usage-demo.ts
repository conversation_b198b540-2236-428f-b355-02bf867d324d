/**
 * Meta Conversions API 事件上报系统使用演示
 * 
 * 这个文件展示了如何在实际项目中使用Meta事件上报系统
 */

import {
  createMetaReporter,
  MetaEventName,
  type MetaReportingService,
  type UserInfo
} from '../src'

// 模拟用户数据
const mockUserInfo: UserInfo = {
  uuid: 'user-123-456-789',
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+1234567890',
  gender: 'male',
  role: 'user',
  create_time: '2024-01-01T00:00:00Z'
}

// 模拟认证token
const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'

// 创建Meta事件上报器
const metaReporter = createMetaReporter({
  apiBase: 'https://api.example.com',
  authTokenGetter: () => mockToken,
  userDataGetter: () => mockUserInfo,
  debug: true,
  retryConfig: {
    maxRetries: 3,
    retryDelay: 1000,
    backoffMultiplier: 2
  },
  batchConfig: {
    maxBatchSize: 10,
    flushInterval: 5000
  },
  defaultActionSource: 'website'
})

// 演示函数
export class MetaTrackingDemo {
  /**
   * 演示购买事件上报
   */
  static async demoPurchaseEvent() {
    console.log('🛒 演示购买事件上报...')
    
    const result = await metaReporter.reportEvent(MetaEventName.Purchase, {
      currency: 'usd',
      value: 29.99,
      order_id: 'order-12345',
      contents: [
        {
          id: 'premium-story-pack',
          quantity: 1,
          item_price: 29.99,
          title: 'Premium Story Pack',
          category: 'digital-content',
          delivery_category: 'home_delivery'
        }
      ]
    })
    
    console.log('购买事件上报结果:', result)
    return result
  }

  /**
   * 演示注册完成事件上报
   */
  static async demoRegistrationEvent() {
    console.log('📝 演示注册完成事件上报...')
    
    const result = await metaReporter.reportEvent(MetaEventName.CompleteRegistration, {
      currency: 'usd',
      value: 0,
      content_name: 'User Registration'
    })
    
    console.log('注册事件上报结果:', result)
    return result
  }

  /**
   * 演示内容查看事件上报
   */
  static async demoViewContentEvent() {
    console.log('👀 演示内容查看事件上报...')
    
    const result = await metaReporter.reportEvent(MetaEventName.ViewContent, {
      content_type: 'story',
      content_ids: ['story-romance-001'],
      content_name: 'Romantic Adventure Story',
      content_category: 'romance'
    })
    
    console.log('内容查看事件上报结果:', result)
    return result
  }

  /**
   * 演示搜索事件上报
   */
  static async demoSearchEvent() {
    console.log('🔍 演示搜索事件上报...')
    
    const result = await metaReporter.reportEvent(MetaEventName.Search, {
      search_string: 'romantic stories',
      content_category: 'romance'
    })
    
    console.log('搜索事件上报结果:', result)
    return result
  }

  /**
   * 演示添加到购物车事件上报
   */
  static async demoAddToCartEvent() {
    console.log('🛍️ 演示添加到购物车事件上报...')
    
    const result = await metaReporter.reportEvent(MetaEventName.AddToCart, {
      content_ids: ['premium-coins-pack'],
      content_name: '1000 Premium Coins',
      currency: 'usd',
      value: 9.99
    })
    
    console.log('添加到购物车事件上报结果:', result)
    return result
  }

  /**
   * 演示发起结账事件上报
   */
  static async demoInitiateCheckoutEvent() {
    console.log('💳 演示发起结账事件上报...')
    
    const result = await metaReporter.reportEvent(MetaEventName.InitiateCheckout, {
      currency: 'usd',
      value: 39.98,
      num_items: 2,
      contents: [
        {
          id: 'premium-story-pack',
          quantity: 1,
          item_price: 29.99
        },
        {
          id: 'premium-coins-pack',
          quantity: 1,
          item_price: 9.99
        }
      ]
    })
    
    console.log('发起结账事件上报结果:', result)
    return result
  }

  /**
   * 演示订阅事件上报
   */
  static async demoSubscribeEvent() {
    console.log('📅 演示订阅事件上报...')
    
    const result = await metaReporter.reportEvent(MetaEventName.Subscribe, {
      currency: 'usd',
      value: 19.99,
      content_name: 'Monthly Premium Subscription',
      predicted_ltv: 239.88 // 预测年度价值
    })
    
    console.log('订阅事件上报结果:', result)
    return result
  }

  /**
   * 演示批量事件上报
   */
  static async demoBatchEvents() {
    console.log('📦 演示批量事件上报...')
    
    const results = await metaReporter.reportEvents([
      {
        eventName: MetaEventName.ViewContent,
        customData: {
          content_type: 'story',
          content_ids: ['story-001'],
          content_name: 'Story 1'
        }
      },
      {
        eventName: MetaEventName.ViewContent,
        customData: {
          content_type: 'story',
          content_ids: ['story-002'],
          content_name: 'Story 2'
        }
      },
      {
        eventName: MetaEventName.Search,
        customData: {
          search_string: 'adventure stories'
        }
      }
    ])
    
    console.log('批量事件上报结果:', results)
    return results
  }

  /**
   * 演示队列状态查询
   */
  static demoQueueStatus() {
    console.log('📊 查询队列状态...')
    
    const status = metaReporter.getQueueStatus()
    console.log('队列状态:', status)
    return status
  }

  /**
   * 运行所有演示
   */
  static async runAllDemos() {
    console.log('🚀 开始运行Meta事件上报演示...\n')
    
    try {
      // 依次运行各种事件演示
      await this.demoViewContentEvent()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await this.demoSearchEvent()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await this.demoAddToCartEvent()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await this.demoInitiateCheckoutEvent()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await this.demoPurchaseEvent()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await this.demoRegistrationEvent()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await this.demoSubscribeEvent()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await this.demoBatchEvents()
      
      // 查询最终队列状态
      this.demoQueueStatus()
      
      console.log('\n✅ 所有演示完成！')
    } catch (error) {
      console.error('❌ 演示过程中出现错误:', error)
    }
  }
}

// 如果直接运行此文件，执行演示
if (typeof window === 'undefined' && require.main === module) {
  MetaTrackingDemo.runAllDemos()
}
