/**
 * Nuxt App 中的 Meta 事件上报集成示例
 */

import { createMetaReporter, MetaEventName, type MetaReportingService } from '../src'

// 1. 创建 composable
export const useMetaTracking = () => {
  const config = useRuntimeConfig()
  const userStore = useUserStore()
  
  // 创建Meta事件上报器
  const metaReporter = createMetaReporter({
    apiBase: config.public.apiBase as string,
    authTokenGetter: () => userStore.token,
    userDataGetter: () => userStore.userInfo,
    debug: config.public.debug as boolean,
    retryConfig: {
      maxRetries: 3,
      retryDelay: 2000,
      backoffMultiplier: 2
    },
    batchConfig: {
      maxBatchSize: 5,
      flushInterval: 3000
    }
  })

  // 购买事件跟踪
  const trackPurchase = async (params: {
    value: number
    currency?: string
    orderId?: string
    contents?: Array<{
      id: string
      quantity: number
      item_price?: number
    }>
  }) => {
    return await metaReporter.reportEvent(MetaEventName.Purchase, {
      currency: params.currency || 'usd',
      value: params.value,
      order_id: params.orderId,
      contents: params.contents?.map(item => ({
        id: item.id,
        quantity: item.quantity,
        item_price: item.item_price,
        delivery_category: 'home_delivery' as const
      }))
    })
  }

  // 注册完成事件跟踪
  const trackRegistration = async () => {
    return await metaReporter.reportEvent(MetaEventName.CompleteRegistration, {
      currency: 'usd',
      value: 0
    })
  }

  // 故事查看事件跟踪
  const trackStoryView = async (params: {
    storyId: string
    storyTitle: string
    category?: string
  }) => {
    return await metaReporter.reportEvent(MetaEventName.ViewContent, {
      content_type: 'story',
      content_ids: [params.storyId],
      content_name: params.storyTitle,
      content_category: params.category
    })
  }

  // 搜索事件跟踪
  const trackSearch = async (searchString: string) => {
    return await metaReporter.reportEvent(MetaEventName.Search, {
      search_string: searchString
    })
  }

  // 添加到购物车事件跟踪
  const trackAddToCart = async (params: {
    contentId: string
    contentName: string
    value: number
    currency?: string
  }) => {
    return await metaReporter.reportEvent(MetaEventName.AddToCart, {
      content_ids: [params.contentId],
      content_name: params.contentName,
      currency: params.currency || 'usd',
      value: params.value
    })
  }

  // 发起结账事件跟踪
  const trackInitiateCheckout = async (params: {
    value: number
    currency?: string
    numItems?: number
  }) => {
    return await metaReporter.reportEvent(MetaEventName.InitiateCheckout, {
      currency: params.currency || 'usd',
      value: params.value,
      num_items: params.numItems
    })
  }

  // 订阅事件跟踪
  const trackSubscribe = async (params: {
    value: number
    currency?: string
    subscriptionType?: string
  }) => {
    return await metaReporter.reportEvent(MetaEventName.Subscribe, {
      currency: params.currency || 'usd',
      value: params.value,
      content_name: params.subscriptionType
    })
  }

  // 获取队列状态
  const getQueueStatus = () => {
    return metaReporter.getQueueStatus()
  }

  return {
    metaReporter,
    trackPurchase,
    trackRegistration,
    trackStoryView,
    trackSearch,
    trackAddToCart,
    trackInitiateCheckout,
    trackSubscribe,
    getQueueStatus
  }
}

// 2. 在页面中使用示例
/*
// pages/story/[id].vue
<script setup>
const route = useRoute()
const { trackStoryView } = useMetaTracking()

// 获取故事数据
const { data: story } = await useFetch(`/api/v1/story.get?id=${route.params.id}`)

// 跟踪故事查看事件
onMounted(() => {
  if (story.value) {
    trackStoryView({
      storyId: story.value.id,
      storyTitle: story.value.title,
      category: story.value.category
    })
  }
})
</script>
*/

// 3. 在支付成功页面使用
/*
// pages/payment-success.vue
<script setup>
const route = useRoute()
const { trackPurchase } = useMetaTracking()

onMounted(() => {
  const orderId = route.query.order_id as string
  const amount = parseFloat(route.query.amount as string)
  
  if (orderId && amount) {
    trackPurchase({
      value: amount,
      currency: 'usd',
      orderId,
      contents: [
        {
          id: route.query.product_id as string,
          quantity: 1,
          item_price: amount
        }
      ]
    })
  }
})
</script>
*/

// 4. 在搜索页面使用
/*
// pages/search.vue
<script setup>
const route = useRoute()
const { trackSearch } = useMetaTracking()

watch(() => route.query.q, (searchQuery) => {
  if (searchQuery && typeof searchQuery === 'string') {
    trackSearch(searchQuery)
  }
}, { immediate: true })
</script>
*/
