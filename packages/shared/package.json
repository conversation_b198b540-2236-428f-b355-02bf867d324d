{"name": "shared", "version": "1.0.0", "description": "Shared utilities and services for Magic Partner monorepo", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint src --ext .ts,.js", "type:check": "tsc --noEmit"}, "keywords": ["shared", "meta", "conversions-api", "event-reporting"], "author": "Magic Partner Team", "license": "MIT", "dependencies": {}, "devDependencies": {"typescript": "5.9.0-beta", "tsup": "^7.0.0", "eslint": "^8.0.0"}, "peerDependencies": {"typescript": "5.9.0-beta"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"]}