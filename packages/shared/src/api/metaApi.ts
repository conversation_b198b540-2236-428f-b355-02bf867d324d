/**
 * Meta Conversions API 客户端
 * 负责与后端 /api/v1/meta-api.forward 接口通信
 */

import type { MetaEventData, MetaApiResponse } from '../types/meta'

export interface MetaApiClientConfig {
  /** API基础URL */
  apiBase: string
  /** 获取认证token的函数 */
  authTokenGetter?: () => string | null
  /** 请求超时时间 (毫秒) */
  timeout?: number
  /** 调试模式 */
  debug?: boolean
}

export class MetaApiClient {
  private config: MetaApiClientConfig

  constructor(config: MetaApiClientConfig) {
    this.config = {
      timeout: 30000,
      debug: false,
      ...config,
    }
  }

  /**
   * 发送单个事件到Meta Conversions API
   * @param eventData 事件数据
   * @returns API响应
   */
  async sendEvent(eventData: MetaEventData): Promise<MetaApiResponse> {
    return this.sendEvents([eventData])
  }

  /**
   * 批量发送事件到Meta Conversions API
   * @param events 事件数据数组
   * @returns API响应
   */
  async sendEvents(events: MetaEventData[]): Promise<MetaApiResponse> {
    if (events.length === 0) {
      throw new Error('No events to send')
    }

    const url = `${this.config.apiBase}/api/v1/meta-api.forward`

    // 准备请求头
    const headers: Record<string, string> = {}

    // 添加认证头
    const token = this.config.authTokenGetter?.()
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    // 准备FormData请求体
    const formData = new FormData()

    // 将事件数据作为JSON字符串添加到data字段
    formData.append('data', JSON.stringify(events))

    if (this.config.debug) {
      console.log('Meta API Request:', {
        url,
        headers: {
          ...headers,
          Authorization: headers.Authorization ? '[REDACTED]' : undefined,
        },
        formData: this.formDataToObject(formData),
      })
    }

    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(
        () => controller.abort(),
        this.config.timeout,
      )

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: formData,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: MetaApiResponse = await response.json()

      if (this.config.debug) {
        console.log('Meta API Response:', result)
      }

      return result
    } catch (error) {
      if (this.config.debug) {
        console.error('Meta API Error:', error)
      }

      // 处理不同类型的错误
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Request timeout')
        }
        throw error
      }

      throw new Error('Unknown error occurred')
    }
  }

  /**
   * 将FormData转换为普通对象用于调试
   * @param formData FormData对象
   * @returns 普通对象
   */
  private formDataToObject(formData: FormData): Record<string, any> {
    const obj: Record<string, any> = {}
    for (const [key, value] of formData.entries()) {
      if (typeof value === 'string' && key === 'data') {
        try {
          // 解析data字段的JSON字符串
          obj[key] = JSON.parse(value)
        } catch {
          // 如果解析失败，直接使用字符串值
          obj[key] = value
        }
      } else {
        obj[key] = value
      }
    }
    return obj
  }

  /**
   * 测试API连接
   * @returns 是否连接成功
   */
  async testConnection(): Promise<boolean> {
    try {
      // 发送一个测试事件
      const testEvent: MetaEventData = {
        event_name: 'PageView',
        event_time: Math.floor(Date.now() / 1000),
        user_data: {
          client_user_agent: 'test-agent',
        },
        action_source: 'website',
        event_source_url: 'https://test.example.com',
      }

      const response = await this.sendEvent(testEvent)
      return response.code === '0' || response.code === 'success'
    } catch (error) {
      if (this.config.debug) {
        console.error('Connection test failed:', error)
      }
      return false
    }
  }

  /**
   * 更新配置
   * @param newConfig 新配置
   */
  updateConfig(newConfig: Partial<MetaApiClientConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取当前配置
   * @returns 当前配置
   */
  getConfig(): MetaApiClientConfig {
    return { ...this.config }
  }
}

/**
 * 创建Meta API客户端实例
 * @param config 配置
 * @returns API客户端实例
 */
export function createMetaApiClient(
  config: MetaApiClientConfig,
): MetaApiClient {
  return new MetaApiClient(config)
}
