/**
 * Shared utilities and services for Magic Partner monorepo
 * 
 * 主要功能：
 * - Meta Conversions API 事件上报
 * - 用户数据处理和加密
 * - 事件队列和重试机制
 */

// 导出所有类型定义
export * from './types'

// 导出工具函数
export * from './utils'

// 导出API客户端
export * from './api'

// 导出服务
export * from './services'

// 导出便捷函数
export { createMetaReporter } from './services/metaReporting'
export { createMetaApiClient } from './api/metaApi'

// 导出常用的工具函数
export {
  hashEmail,
  hashPhone,
  hashName,
  isValidEmail,
  isValidPhone
} from './utils/crypto'

export {
  buildMetaUserData,
  generateEventId,
  getCurrentPageUrl,
  isOptedOut,
  setOptOut,
  validateUserData
} from './utils/userData'
