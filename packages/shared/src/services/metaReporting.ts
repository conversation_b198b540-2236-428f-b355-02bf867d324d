/**
 * Meta Conversions API 事件上报服务
 * 提供事件队列、重试机制和批量发送功能
 */

import { MetaApiClient, createMetaApiClient } from '../api/metaApi'
import {
  buildMetaUserData,
  generateEventId,
  getCurrentPageUrl,
  isOptedOut,
  validateUserData,
} from '../utils/userData'
import type {
  MetaEventData,
  MetaEventName,
  MetaCustomData,
  MetaReportingConfig,
  MetaEventQueueItem,
  MetaReportingResult,
  UserInfo,
} from '../types/meta'

export class MetaReportingService {
  private apiClient: MetaApiClient
  private config: MetaReportingConfig & {
    retryConfig: NonNullable<MetaReportingConfig['retryConfig']>
    batchConfig: NonNullable<MetaReportingConfig['batchConfig']>
    debug: boolean
    defaultActionSource: NonNullable<MetaReportingConfig['defaultActionSource']>
  }
  private eventQueue: MetaEventQueueItem[] = []
  private isProcessing = false
  private flushTimer?: number

  constructor(config: MetaReportingConfig) {
    // 设置默认配置
    this.config = {
      ...config,
      debug: config.debug ?? false,
      retryConfig: {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2,
        ...config.retryConfig,
      },
      batchConfig: {
        maxBatchSize: 10,
        flushInterval: 5000,
        ...config.batchConfig,
      },
      defaultActionSource: config.defaultActionSource ?? 'website',
    }

    // 创建API客户端
    this.apiClient = createMetaApiClient({
      apiBase: this.config.apiBase,
      authTokenGetter: this.config.authTokenGetter,
      debug: this.config.debug,
    })

    // 启动定时刷新
    this.startFlushTimer()
  }

  /**
   * 上报单个事件
   * @param eventName 事件名称
   * @param customData 自定义数据
   * @param options 选项
   * @returns 上报结果
   */
  async reportEvent(
    eventName: MetaEventName | string,
    customData?: MetaCustomData,
    options?: {
      actionSource?: MetaEventData['action_source']
      eventSourceUrl?: string
      eventId?: string
      immediate?: boolean
    },
  ): Promise<MetaReportingResult> {
    try {
      // 检查用户是否选择退出
      if (isOptedOut()) {
        if (this.config.debug) {
          console.log('User opted out of tracking, skipping event:', eventName)
        }
        return { success: true, eventId: 'opted-out' }
      }

      // 获取用户数据
      const userInfo = this.config.userDataGetter?.()
      const userData = await buildMetaUserData(userInfo)

      // 验证用户数据
      const validation = validateUserData(userData)
      if (!validation.isValid && this.config.debug) {
        console.warn('User data validation warnings:', validation.warnings)
      }

      // 构建事件数据 - 按照工作示例的字段顺序
      const eventData: MetaEventData = {
        event_name: eventName,
        event_time: Math.floor(Date.now() / 1000),
        user_data: userData,
        custom_data: customData,
        event_source_url: options?.eventSourceUrl || getCurrentPageUrl(),
        action_source: options?.actionSource || this.config.defaultActionSource,
        event_id:
          options?.eventId || generateEventId(eventName, userInfo?.uuid),
      }

      if (this.config.debug) {
        console.log('Reporting Meta event:', eventData)
      }

      // 如果要求立即发送
      if (options?.immediate) {
        return await this.sendEventImmediately(eventData)
      }

      // 添加到队列
      this.addToQueue(eventData)
      return { success: true, eventId: eventData.event_id }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'
      if (this.config.debug) {
        console.error('Failed to report Meta event:', error)
      }
      return { success: false, error: errorMessage }
    }
  }

  /**
   * 批量上报事件
   * @param events 事件数组
   * @returns 上报结果
   */
  async reportEvents(
    events: Array<{
      eventName: MetaEventName | string
      customData?: MetaCustomData
      options?: {
        actionSource?: MetaEventData['action_source']
        eventSourceUrl?: string
        eventId?: string
      }
    }>,
  ): Promise<MetaReportingResult[]> {
    const results: MetaReportingResult[] = []

    for (const event of events) {
      const result = await this.reportEvent(event.eventName, event.customData, {
        ...event.options,
        immediate: false,
      })
      results.push(result)
    }

    // 立即刷新队列
    await this.flushQueue()
    return results
  }

  /**
   * 立即发送事件
   * @param eventData 事件数据
   * @returns 发送结果
   */
  private async sendEventImmediately(
    eventData: MetaEventData,
  ): Promise<MetaReportingResult> {
    try {
      const response = await this.apiClient.sendEvent(eventData)

      if (response.code === '0' || response.code === 'success') {
        return {
          success: true,
          eventId: eventData.event_id,
          response,
        }
      } else {
        return {
          success: false,
          error: response.message || 'API returned error',
          response,
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'
      return {
        success: false,
        error: errorMessage,
      }
    }
  }

  /**
   * 添加事件到队列
   * @param eventData 事件数据
   */
  private addToQueue(eventData: MetaEventData): void {
    const queueItem: MetaEventQueueItem = {
      eventData,
      retryCount: 0,
      createdAt: Date.now(),
    }

    this.eventQueue.push(queueItem)

    // 如果队列达到最大大小，立即刷新
    if (this.eventQueue.length >= this.config.batchConfig.maxBatchSize) {
      this.flushQueue()
    }
  }

  /**
   * 刷新事件队列
   */
  async flushQueue(): Promise<void> {
    if (this.isProcessing || this.eventQueue.length === 0) {
      return
    }

    this.isProcessing = true

    try {
      // 取出要发送的事件
      const eventsToSend = this.eventQueue.splice(
        0,
        this.config.batchConfig.maxBatchSize,
      )
      const eventDataArray = eventsToSend.map((item) => item.eventData)

      if (this.config.debug) {
        console.log(`Flushing ${eventDataArray.length} Meta events`)
      }

      // 发送事件
      const response = await this.apiClient.sendEvents(eventDataArray)

      if (response.code !== '0' && response.code !== 'success') {
        // 发送失败，重新加入队列进行重试
        this.handleFailedEvents(eventsToSend, response.message || 'API error')
      }
    } catch (error) {
      // 网络错误或其他异常，重新加入队列
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'
      if (this.config.debug) {
        console.error('Failed to flush Meta events:', error)
      }
      // 这里需要重新处理失败的事件，但由于已经从队列中移除，需要特殊处理
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * 处理发送失败的事件
   * @param failedEvents 失败的事件
   * @param error 错误信息
   */
  private handleFailedEvents(
    failedEvents: MetaEventQueueItem[],
    error: string,
  ): void {
    for (const item of failedEvents) {
      if (item.retryCount < this.config.retryConfig.maxRetries) {
        // 增加重试次数并重新加入队列
        item.retryCount++
        item.nextRetryAt =
          Date.now() +
          this.config.retryConfig.retryDelay *
            Math.pow(
              this.config.retryConfig.backoffMultiplier,
              item.retryCount - 1,
            )

        this.eventQueue.unshift(item) // 添加到队列前面优先处理
      } else {
        if (this.config.debug) {
          console.error(
            `Meta event failed after ${this.config.retryConfig.maxRetries} retries:`,
            {
              event: item.eventData.event_name,
              error,
            },
          )
        }
      }
    }
  }

  /**
   * 启动定时刷新
   */
  private startFlushTimer(): void {
    if (typeof window !== 'undefined') {
      this.flushTimer = window.setInterval(() => {
        this.flushQueue()
      }, this.config.batchConfig.flushInterval)
    }
  }

  /**
   * 停止定时刷新
   */
  private stopFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
      this.flushTimer = undefined
    }
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): {
    queueLength: number
    isProcessing: boolean
    pendingRetries: number
  } {
    const pendingRetries = this.eventQueue.filter(
      (item) => item.retryCount > 0,
    ).length

    return {
      queueLength: this.eventQueue.length,
      isProcessing: this.isProcessing,
      pendingRetries,
    }
  }

  /**
   * 清空队列
   */
  clearQueue(): void {
    this.eventQueue = []
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stopFlushTimer()
    this.clearQueue()
  }
}

/**
 * 创建Meta事件上报服务实例
 * @param config 配置
 * @returns 服务实例
 */
export function createMetaReporter(
  config: MetaReportingConfig,
): MetaReportingService {
  return new MetaReportingService(config)
}
