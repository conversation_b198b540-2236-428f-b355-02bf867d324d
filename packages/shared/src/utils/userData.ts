/**
 * 用户数据处理工具
 * 用于构建Meta Conversions API所需的用户数据
 */

import {
  hashEmail,
  hashPhone,
  hashName,
  isValidEmail,
  isValidPhone,
} from './crypto'
import type { MetaUserData, UserInfo } from '../types/meta'

/**
 * 获取客户端IP地址
 * 注意：在浏览器环境中无法直接获取真实IP，需要后端配合
 */
export function getClientIpAddress(): string | undefined {
  // 在浏览器环境中，这个值通常由后端在请求头中提供
  // 这里返回undefined，让后端处理
  return undefined
}

/**
 * 获取客户端User Agent
 */
export function getClientUserAgent(): string {
  if (typeof navigator !== 'undefined') {
    return navigator.userAgent
  }
  return 'Unknown'
}

/**
 * 获取Facebook Click ID (fbc)
 * 从URL参数或cookie中获取
 */
export function getFacebookClickId(): string | undefined {
  if (typeof window === 'undefined') {
    return undefined
  }

  // 首先尝试从URL参数获取
  const urlParams = new URLSearchParams(window.location.search)
  const fbclid = urlParams.get('fbclid')

  if (fbclid) {
    // 构建fbc格式: fb.{version}.{timestamp}.{fbclid}
    const timestamp = Date.now()
    return `fb.1.${timestamp}.${fbclid}`
  }

  // 尝试从cookie获取
  const cookies = document.cookie.split(';')
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=')
    if (name === '_fbc') {
      return value
    }
  }

  return undefined
}

/**
 * 获取Facebook Browser ID (fbp)
 * 从cookie中获取
 */
export function getFacebookBrowserId(): string | undefined {
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return undefined
  }

  const cookies = document.cookie.split(';')
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=')
    if (name === '_fbp') {
      return value
    }
  }

  return undefined
}

/**
 * 从用户信息构建Meta用户数据
 * @param userInfo 用户信息
 * @returns Meta用户数据
 */
export async function buildMetaUserData(
  userInfo?: UserInfo,
): Promise<MetaUserData> {
  // 按照工作示例的字段顺序构建用户数据
  const userData: MetaUserData = {}

  // 1. 邮箱哈希 (em) - 优先级最高
  if (userInfo?.email && isValidEmail(userInfo.email)) {
    try {
      const hashedEmail = await hashEmail(userInfo.email)
      userData.em = [hashedEmail]
    } catch (error) {
      console.warn('Failed to hash email:', error)
    }
  }

  // 2. 电话号码哈希 (ph) - 如果没有邮箱，电话号码很重要
  if (userInfo?.phone && isValidPhone(userInfo.phone)) {
    try {
      const hashedPhone = await hashPhone(userInfo.phone)
      userData.ph = [hashedPhone]
    } catch (error) {
      console.warn('Failed to hash phone:', error)
    }
  }

  // 3. 客户端IP地址 - 重要字段，尽量提供
  const clientIp = getClientIpAddress()
  if (clientIp) {
    userData.client_ip_address = clientIp
  } else {
    // 如果无法获取真实IP，提供一个占位符
    userData.client_ip_address = '127.0.0.1'
  }

  // 4. 客户端User Agent - 必需字段
  userData.client_user_agent = getClientUserAgent()

  // 5. Facebook Click ID (fbc) - 重要的跟踪字段
  const fbc = getFacebookClickId()
  if (fbc) {
    userData.fbc = fbc
  } else {
    // 如果没有真实的fbc，生成一个格式正确的占位符
    const timestamp = Date.now()
    userData.fbc = `fb.1.${timestamp}.placeholder`
  }

  // 6. Facebook Browser ID (fbp) - 重要的跟踪字段
  const fbp = getFacebookBrowserId()
  if (fbp) {
    userData.fbp = fbp
  } else {
    // 如果没有真实的fbp，生成一个格式正确的占位符
    const timestamp = Date.now()
    const randomId = Math.random().toString().substring(2, 15)
    userData.fbp = `fb.1.${timestamp}.${randomId}`
  }

  // 7. 外部ID (用户UUID)
  if (userInfo?.uuid) {
    userData.external_id = [userInfo.uuid]
  }

  // 8. 姓名哈希
  if (userInfo?.name) {
    try {
      const hashedName = await hashName(userInfo.name)
      userData.fn = [hashedName]
    } catch (error) {
      console.warn('Failed to hash name:', error)
    }
  }

  // 9. 性别
  if (userInfo?.gender) {
    const genderMap: Record<string, string> = {
      male: 'm',
      female: 'f',
      unknown: '',
    }
    const mappedGender = genderMap[userInfo.gender.toLowerCase()]
    if (mappedGender) {
      userData.ge = [mappedGender]
    }
  }

  return userData
}

/**
 * 生成事件ID用于去重
 * @param eventName 事件名称
 * @param userId 用户ID
 * @param timestamp 时间戳
 * @returns 事件ID
 */
export function generateEventId(
  eventName: string,
  userId?: string,
  timestamp?: number,
): string {
  const ts = timestamp || Date.now()
  const uid = userId || 'anonymous'
  const random = Math.random().toString(36).substring(2, 8)
  return `${eventName}_${uid}_${ts}_${random}`
}

/**
 * 获取当前页面URL
 */
export function getCurrentPageUrl(): string {
  if (typeof window !== 'undefined') {
    return window.location.href
  }
  return ''
}

/**
 * 检查用户是否选择退出跟踪
 */
export function isOptedOut(): boolean {
  if (typeof window === 'undefined') {
    return false
  }

  // 检查Do Not Track设置
  if (navigator.doNotTrack === '1') {
    return true
  }

  // 检查本地存储的选择退出设置
  try {
    const optOut = localStorage.getItem('meta_tracking_opt_out')
    return optOut === 'true'
  } catch (error) {
    return false
  }
}

/**
 * 设置用户选择退出跟踪
 * @param optOut 是否选择退出
 */
export function setOptOut(optOut: boolean): void {
  if (typeof window === 'undefined') {
    return
  }

  try {
    localStorage.setItem('meta_tracking_opt_out', optOut.toString())
  } catch (error) {
    console.warn('Failed to set opt-out preference:', error)
  }
}

/**
 * 验证用户数据的完整性
 * @param userData Meta用户数据
 * @returns 验证结果
 */
export function validateUserData(userData: MetaUserData): {
  isValid: boolean
  warnings: string[]
} {
  const warnings: string[] = []

  // 检查是否有任何用户标识符
  const hasIdentifier = !!(
    userData.em?.length ||
    userData.ph?.length ||
    userData.external_id?.length ||
    userData.fbc ||
    userData.fbp
  )

  if (!hasIdentifier) {
    warnings.push(
      'No user identifiers found (email, phone, external_id, fbc, or fbp)',
    )
  }

  // 检查User Agent
  if (!userData.client_user_agent) {
    warnings.push('Missing client_user_agent')
  }

  return {
    isValid: hasIdentifier,
    warnings,
  }
}
