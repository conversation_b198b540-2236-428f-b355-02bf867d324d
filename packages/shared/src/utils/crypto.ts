/**
 * 加密工具函数
 * 用于处理Meta Conversions API要求的数据哈希
 */

/**
 * 计算字符串的SHA256哈希值
 * @param input 输入字符串
 * @returns SHA256哈希值
 */
export async function sha256Hash(input: string): Promise<string> {
  // 标准化输入：去除空格并转为小写
  const normalizedInput = input.trim().toLowerCase()
  
  if (!normalizedInput) {
    throw new Error('Input cannot be empty')
  }

  // 检查是否在浏览器环境
  if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
    // 浏览器环境使用Web Crypto API
    const encoder = new TextEncoder()
    const data = encoder.encode(normalizedInput)
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  } else {
    // Node.js环境或不支持Web Crypto API的浏览器
    // 这里提供一个简单的fallback实现
    // 在生产环境中，建议使用专门的crypto库
    console.warn('Web Crypto API not available, using fallback hash implementation')
    return await fallbackHash(normalizedInput)
  }
}

/**
 * Fallback哈希实现 (仅用于开发环境)
 * 注意：这不是真正的SHA256，仅用于开发测试
 */
async function fallbackHash(input: string): Promise<string> {
  let hash = 0
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  // 转换为16进制并填充到64位
  return Math.abs(hash).toString(16).padStart(16, '0').repeat(4).substring(0, 64)
}

/**
 * 哈希邮箱地址
 * @param email 邮箱地址
 * @returns 哈希后的邮箱
 */
export async function hashEmail(email: string): Promise<string> {
  if (!email || !isValidEmail(email)) {
    throw new Error('Invalid email address')
  }
  return await sha256Hash(email)
}

/**
 * 哈希电话号码
 * @param phone 电话号码
 * @returns 哈希后的电话号码
 */
export async function hashPhone(phone: string): Promise<string> {
  if (!phone) {
    throw new Error('Phone number cannot be empty')
  }
  
  // 标准化电话号码：只保留数字
  const normalizedPhone = phone.replace(/\D/g, '')
  
  if (normalizedPhone.length < 10) {
    throw new Error('Phone number too short')
  }
  
  return await sha256Hash(normalizedPhone)
}

/**
 * 哈希姓名
 * @param name 姓名
 * @returns 哈希后的姓名
 */
export async function hashName(name: string): Promise<string> {
  if (!name) {
    throw new Error('Name cannot be empty')
  }
  return await sha256Hash(name)
}

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @returns 是否为有效邮箱
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return emailRegex.test(email)
}

/**
 * 验证电话号码格式
 * @param phone 电话号码
 * @returns 是否为有效电话号码
 */
export function isValidPhone(phone: string): boolean {
  const normalizedPhone = phone.replace(/\D/g, '')
  return normalizedPhone.length >= 10 && normalizedPhone.length <= 15
}

/**
 * 批量哈希处理
 * @param values 要哈希的值数组
 * @param hashFunction 哈希函数
 * @returns 哈希后的值数组
 */
export async function batchHash(
  values: string[],
  hashFunction: (value: string) => Promise<string>
): Promise<string[]> {
  const results: string[] = []
  
  for (const value of values) {
    try {
      const hashed = await hashFunction(value)
      results.push(hashed)
    } catch (error) {
      console.warn(`Failed to hash value: ${error}`)
      // 跳过无效值，不添加到结果中
    }
  }
  
  return results
}
