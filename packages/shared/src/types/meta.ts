/**
 * Meta Conversions API 事件上报类型定义
 */

// Meta 事件名称枚举
export enum MetaEventName {
  Purchase = 'Purchase',
  ViewContent = 'ViewContent',
  AddToCart = 'AddToCart',
  InitiateCheckout = 'InitiateCheckout',
  CompleteRegistration = 'CompleteRegistration',
  Lead = 'Lead',
  Search = 'Search',
  AddPaymentInfo = 'AddPaymentInfo',
  AddToWishlist = 'AddToWishlist',
  Contact = 'Contact',
  CustomizeProduct = 'CustomizeProduct',
  Donate = 'Donate',
  FindLocation = 'FindLocation',
  Schedule = 'Schedule',
  StartTrial = 'StartTrial',
  SubmitApplication = 'SubmitApplication',
  Subscribe = 'Subscribe'
}

// 用户数据接口
export interface MetaUserData {
  /** 邮箱哈希值数组 (SHA256) */
  em?: string[]
  /** 电话号码哈希值数组 (SHA256) */
  ph?: string[]
  /** 客户端IP地址 */
  client_ip_address?: string
  /** 客户端User Agent */
  client_user_agent?: string
  /** Facebook Click ID */
  fbc?: string
  /** Facebook Browser ID */
  fbp?: string
  /** 外部ID (用户UUID) */
  external_id?: string[]
  /** 名字哈希值 */
  fn?: string[]
  /** 姓氏哈希值 */
  ln?: string[]
  /** 生日哈希值 */
  db?: string[]
  /** 性别 */
  ge?: string[]
  /** 城市哈希值 */
  ct?: string[]
  /** 州/省哈希值 */
  st?: string[]
  /** 邮编哈希值 */
  zp?: string[]
  /** 国家代码哈希值 */
  country?: string[]
}

// 内容项接口
export interface MetaContentItem {
  /** 产品ID */
  id: string
  /** 数量 */
  quantity?: number
  /** 单价 */
  item_price?: number
  /** 标题 */
  title?: string
  /** 描述 */
  description?: string
  /** 品牌 */
  brand?: string
  /** 类别 */
  category?: string
  /** 配送类别 */
  delivery_category?: 'home_delivery' | 'curbside' | 'in_store'
}

// 自定义数据接口
export interface MetaCustomData {
  /** 货币代码 */
  currency?: string
  /** 总价值 */
  value?: number
  /** 内容项数组 */
  contents?: MetaContentItem[]
  /** 内容类型 */
  content_type?: string
  /** 内容ID数组 */
  content_ids?: string[]
  /** 内容名称 */
  content_name?: string
  /** 内容类别 */
  content_category?: string
  /** 搜索字符串 */
  search_string?: string
  /** 状态 */
  status?: string
  /** 订单ID */
  order_id?: string
  /** 预测生命周期价值 */
  predicted_ltv?: number
  /** 订阅数量 */
  num_items?: number
}

// Meta 事件数据接口
export interface MetaEventData {
  /** 事件名称 */
  event_name: MetaEventName | string
  /** 事件时间 (Unix时间戳) */
  event_time: number
  /** 用户数据 */
  user_data: MetaUserData
  /** 自定义数据 */
  custom_data?: MetaCustomData
  /** 事件源URL */
  event_source_url?: string
  /** 动作源 */
  action_source: 'website' | 'app' | 'email' | 'chat' | 'phone_call' | 'physical_store' | 'system_generated' | 'other'
  /** 选择退出 */
  opt_out?: boolean
  /** 事件ID (用于去重) */
  event_id?: string
  /** 数据处理选项 */
  data_processing_options?: string[]
  /** 数据处理选项国家 */
  data_processing_options_country?: number
  /** 数据处理选项州 */
  data_processing_options_state?: number
}

// API 响应接口
export interface MetaApiResponse {
  /** 响应代码 */
  code: string
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data?: any
}

// 事件上报配置接口
export interface MetaReportingConfig {
  /** API基础URL */
  apiBase: string
  /** 获取认证token的函数 */
  authTokenGetter?: () => string | null
  /** 获取用户数据的函数 */
  userDataGetter?: () => any
  /** 调试模式 */
  debug?: boolean
  /** 重试配置 */
  retryConfig?: {
    maxRetries: number
    retryDelay: number
    backoffMultiplier: number
  }
  /** 批量发送配置 */
  batchConfig?: {
    maxBatchSize: number
    flushInterval: number
  }
  /** 默认动作源 */
  defaultActionSource?: MetaEventData['action_source']
}

// 事件队列项接口
export interface MetaEventQueueItem {
  /** 事件数据 */
  eventData: MetaEventData
  /** 重试次数 */
  retryCount: number
  /** 创建时间 */
  createdAt: number
  /** 下次重试时间 */
  nextRetryAt?: number
}

// 用户信息接口 (从现有系统获取)
export interface UserInfo {
  uuid?: string
  name?: string
  email?: string
  phone?: string
  gender?: string
  role?: string
  create_time?: string
}

// 事件上报结果接口
export interface MetaReportingResult {
  /** 是否成功 */
  success: boolean
  /** 错误消息 */
  error?: string
  /** 事件ID */
  eventId?: string
  /** 响应数据 */
  response?: MetaApiResponse
}
