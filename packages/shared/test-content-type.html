<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content-Type Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>Content-Type 测试</h1>
    <p>测试不同FormData创建方式的Content-Type差异</p>

    <div class="test-section">
        <h2>方法1: 直接append字符串 (原始方法)</h2>
        <p>这种方法可能产生 application/octet-stream</p>
        <button onclick="testMethod1()">测试方法1</button>
        <div id="log1" class="log"></div>
    </div>

    <div class="test-section">
        <h2>方法2: 使用Blob指定Content-Type (新方法)</h2>
        <p>这种方法应该产生 text/plain; charset=utf-8</p>
        <button onclick="testMethod2()">测试方法2</button>
        <div id="log2" class="log"></div>
    </div>

    <div class="test-section">
        <h2>方法3: 工作示例的完全复制</h2>
        <p>完全按照你提供的工作代码</p>
        <button onclick="testMethod3()">测试方法3</button>
        <div id="log3" class="log"></div>
    </div>

    <script>
        // 测试数据
        const testEventData = [
            {
                event_name: "Purchase",
                event_time: Math.floor(Date.now() / 1000),
                user_data: {
                    em: [
                        "309a0a5c3e211326ae75ca18196d301a9bdbd1a882a4d2569511033da23f0abd"
                    ],
                    ph: [
                        "254aa248acb47dd654ca3ea53f48c2c26d641d23d7e2e93a1ec56258df7674c4",
                        "6f4fcb9deaeadc8f9746ae76d97ce1239e98b404efe5da3ee0b7149740f89ad6"
                    ],
                    client_ip_address: "***************",
                    client_user_agent: navigator.userAgent,
                    fbc: "fb.1.1554763741205.AbCdEfGhIjKlMnOpQrStUvWxYz1234567890",
                    fbp: "fb.1.1558571054389.1098115397"
                },
                custom_data: {
                    currency: "usd",
                    value: 123.45,
                    contents: [
                        {
                            id: "product123",
                            quantity: 1,
                            delivery_category: "home_delivery"
                        }
                    ]
                },
                event_source_url: window.location.href,
                action_source: "website"
            }
        ];

        // 拦截fetch请求来查看Content-Type
        function interceptFetch(method, formData, logElement) {
            logElement.innerHTML = `开始测试 ${method}...\n`;
            
            // 检查FormData内容
            for (let [key, value] of formData.entries()) {
                logElement.innerHTML += `FormData字段: ${key}\n`;
                logElement.innerHTML += `值类型: ${typeof value}\n`;
                if (value instanceof Blob) {
                    logElement.innerHTML += `Blob类型: ${value.type}\n`;
                    logElement.innerHTML += `Blob大小: ${value.size} bytes\n`;
                } else {
                    logElement.innerHTML += `值: ${value.substring(0, 100)}...\n`;
                }
            }

            // 创建一个XMLHttpRequest来查看实际的请求头
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'https://httpbin.org/post', true);
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        logElement.innerHTML += `\n响应状态: ${xhr.status}\n`;
                        logElement.innerHTML += `Content-Type: ${response.headers['Content-Type']}\n`;
                        logElement.innerHTML += `请求体大小: ${response.headers['Content-Length'] || 'unknown'}\n`;
                        
                        if (xhr.status === 200) {
                            logElement.innerHTML += `<span class="success">✅ 请求成功</span>\n`;
                        } else {
                            logElement.innerHTML += `<span class="error">❌ 请求失败</span>\n`;
                        }
                    } catch (e) {
                        logElement.innerHTML += `<span class="error">❌ 解析响应失败: ${e.message}</span>\n`;
                    }
                }
            };

            xhr.onerror = function() {
                logElement.innerHTML += `<span class="error">❌ 网络错误</span>\n`;
            };

            xhr.send(formData);
        }

        // 方法1: 直接append字符串
        function testMethod1() {
            const log = document.getElementById('log1');
            const formData = new FormData();
            formData.append('data', JSON.stringify(testEventData));
            interceptFetch('方法1 (直接字符串)', formData, log);
        }

        // 方法2: 使用Blob
        function testMethod2() {
            const log = document.getElementById('log2');
            const formData = new FormData();
            const jsonBlob = new Blob([JSON.stringify(testEventData)], {
                type: 'text/plain; charset=utf-8'
            });
            formData.append('data', jsonBlob);
            interceptFetch('方法2 (Blob)', formData, log);
        }

        // 方法3: 工作示例
        function testMethod3() {
            const log = document.getElementById('log3');
            
            // 完全按照工作示例
            const eventData = [
                {
                    event_name: "Purchase",
                    event_time: 1754657563,
                    user_data: {
                        em: [
                            "309a0a5c3e211326ae75ca18196d301a9bdbd1a882a4d2569511033da23f0abd"
                        ],
                        ph: [
                            "254aa248acb47dd654ca3ea53f48c2c26d641d23d7e2e93a1ec56258df7674c4",
                            "6f4fcb9deaeadc8f9746ae76d97ce1239e98b404efe5da3ee0b7149740f89ad6"
                        ],
                        client_ip_address: "***************",
                        client_user_agent: navigator.userAgent,
                        fbc: "fb.1.1554763741205.AbCdEfGhIjKlMnOpQrStUvWxYz1234567890",
                        fbp: "fb.1.1558571054389.1098115397"
                    },
                    custom_data: {
                        currency: "usd",
                        value: 123.45,
                        contents: [
                            {
                                id: "product123",
                                quantity: 1,
                                delivery_category: "home_delivery"
                            }
                        ]
                    },
                    event_source_url: "http://jaspers-market.com/product/123",
                    action_source: "website"
                }
            ];

            const formData = new FormData();
            formData.append('data', JSON.stringify(eventData));
            
            interceptFetch('方法3 (工作示例)', formData, log);
        }
    </script>
</body>
</html>
