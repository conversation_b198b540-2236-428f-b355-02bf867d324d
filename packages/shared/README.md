# Shared Package

Magic Partner项目的共享工具包，提供Meta Conversions API事件上报功能。

## 功能特性

- 🚀 **Meta Conversions API集成** - 完整的Meta事件上报系统
- 🔒 **数据安全** - 自动处理用户数据哈希（SHA256）
- 📊 **事件队列** - 支持批量发送和重试机制
- 🎯 **类型安全** - 完整的TypeScript类型定义
- 🔧 **易于集成** - 支持nuxt-app和csr-app

## 安装

```bash
# 在项目根目录
pnpm install

# 构建shared包
cd packages/shared
pnpm build
```

## 快速开始

### 1. 创建Meta事件上报器

```typescript
import { createMetaReporter, MetaEventName } from 'shared'

const metaReporter = createMetaReporter({
  apiBase: 'https://your-api.com',
  authTokenGetter: () => localStorage.getItem('token'),
  userDataGetter: () => userStore.userInfo,
  debug: process.env.NODE_ENV === 'development'
})
```

### 2. 上报事件

```typescript
// 购买事件
await metaReporter.reportEvent(MetaEventName.Purchase, {
  currency: 'usd',
  value: 123.45,
  contents: [
    {
      id: 'product123',
      quantity: 1,
      delivery_category: 'home_delivery'
    }
  ]
})

// 注册事件
await metaReporter.reportEvent(MetaEventName.CompleteRegistration, {
  currency: 'usd',
  value: 0
})

// 内容查看事件
await metaReporter.reportEvent(MetaEventName.ViewContent, {
  content_type: 'story',
  content_ids: ['story-123'],
  content_name: 'Romance Story Title'
})
```

### 3. 批量上报事件

```typescript
await metaReporter.reportEvents([
  {
    eventName: MetaEventName.ViewContent,
    customData: { content_type: 'story', content_ids: ['story-1'] }
  },
  {
    eventName: MetaEventName.Search,
    customData: { search_string: 'romance stories' }
  }
])
```

## 配置选项

```typescript
interface MetaReportingConfig {
  /** API基础URL */
  apiBase: string
  /** 获取认证token的函数 */
  authTokenGetter?: () => string | null
  /** 获取用户数据的函数 */
  userDataGetter?: () => any
  /** 调试模式 */
  debug?: boolean
  /** 重试配置 */
  retryConfig?: {
    maxRetries: number        // 最大重试次数，默认3
    retryDelay: number        // 重试延迟，默认1000ms
    backoffMultiplier: number // 退避倍数，默认2
  }
  /** 批量发送配置 */
  batchConfig?: {
    maxBatchSize: number      // 最大批量大小，默认10
    flushInterval: number     // 刷新间隔，默认5000ms
  }
  /** 默认动作源 */
  defaultActionSource?: 'website' | 'app' | 'email' // 默认'website'
}
```

## 支持的事件类型

- `Purchase` - 购买事件
- `ViewContent` - 内容查看
- `AddToCart` - 添加到购物车
- `InitiateCheckout` - 发起结账
- `CompleteRegistration` - 完成注册
- `Lead` - 潜在客户
- `Search` - 搜索事件
- `AddPaymentInfo` - 添加支付信息
- `Subscribe` - 订阅事件

## 在Nuxt App中使用

```typescript
// composables/useMetaTracking.ts
import { createMetaReporter, MetaEventName } from 'shared'

export const useMetaTracking = () => {
  const config = useRuntimeConfig()
  const userStore = useUserStore()
  
  const metaReporter = createMetaReporter({
    apiBase: config.public.apiBase,
    authTokenGetter: () => userStore.token,
    userDataGetter: () => userStore.userInfo,
    debug: config.public.debug
  })
  
  return {
    trackPurchase: (value: number, currency = 'usd') => {
      return metaReporter.reportEvent(MetaEventName.Purchase, {
        currency,
        value
      })
    },
    trackRegistration: () => {
      return metaReporter.reportEvent(MetaEventName.CompleteRegistration)
    },
    trackStoryView: (storyId: string, storyTitle: string) => {
      return metaReporter.reportEvent(MetaEventName.ViewContent, {
        content_type: 'story',
        content_ids: [storyId],
        content_name: storyTitle
      })
    }
  }
}
```

## 在CSR App中使用

```typescript
// utils/metaTracking.ts
import { createMetaReporter, MetaEventName } from 'shared'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

export const metaReporter = createMetaReporter({
  apiBase: import.meta.env.VITE_API_BASE,
  authTokenGetter: () => userStore.token,
  userDataGetter: () => userStore.userInfo,
  debug: import.meta.env.DEV
})

// 导出便捷函数
export const trackPurchase = (value: number, currency = 'usd') => {
  return metaReporter.reportEvent(MetaEventName.Purchase, {
    currency,
    value
  })
}

export const trackRegistration = () => {
  return metaReporter.reportEvent(MetaEventName.CompleteRegistration)
}
```

## 数据隐私

- 所有敏感用户数据（邮箱、电话）都会自动进行SHA256哈希处理
- 支持用户选择退出跟踪
- 遵循Meta Conversions API的数据处理要求

## API接口

后端需要实现 `/api/v1/meta-api.forward` 接口，接收以下格式的数据：

```json
{
  "event_name": "Purchase",
  "event_time": 1754650877,
  "user_data": {
    "em": ["309a0a5c3e211326ae75ca18196d301a9bdbd1a882a4d2569511033da23f0abd"],
    "ph": ["254aa248acb47dd654ca3ea53f48c2c26d641d23d7e2e93a1ec56258df7674c4"],
    "client_ip_address": "***************",
    "client_user_agent": "$CLIENT_USER_AGENT",
    "fbc": "fb.1.1554763741205.AbCdEfGhIjKlMnOpQrStUvWxYz1234567890",
    "fbp": "fb.1.1558571054389.1098115397"
  },
  "custom_data": {
    "currency": "usd",
    "value": 123.45,
    "contents": [
      {
        "id": "product123",
        "quantity": 1,
        "delivery_category": "home_delivery"
      }
    ]
  },
  "event_source_url": "http://example.com/product/123",
  "action_source": "website"
}
```

## 开发

```bash
# 开发模式
pnpm dev

# 构建
pnpm build

# 类型检查
pnpm type:check
```
