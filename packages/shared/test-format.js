/**
 * 测试我们的Meta事件数据格式是否与工作示例一致
 */

// 模拟浏览器环境
global.navigator = {
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

global.window = {
  location: {
    href: 'http://localhost:3000/meta-tracking-usage',
    search: ''
  }
}

global.document = {
  cookie: '_fbp=fb.1.1754299584910.536789937587781923'
}

// 导入我们的函数
const { buildMetaUserData } = require('./dist/index.js')

async function testDataFormat() {
  console.log('🧪 测试Meta事件数据格式...\n')

  // 模拟用户信息
  const userInfo = {
    uuid: 'e778349f-4a92-4eb6-bc8d-85216cca7f76',
    name: 'Test User',
    email: '<EMAIL>',
    phone: '+1234567890',
    gender: 'male'
  }

  try {
    // 构建用户数据
    const userData = await buildMetaUserData(userInfo)
    
    // 构建完整的事件数据
    const eventData = [{
      event_name: "ViewContent",
      event_time: Math.floor(Date.now() / 1000),
      user_data: userData,
      custom_data: {
        content_type: "story",
        content_ids: ["story-romance-001"],
        content_name: "Romantic Adventure",
        content_category: "romance"
      },
      event_source_url: "http://localhost:3000/meta-tracking-usage",
      action_source: "website",
      event_id: `ViewContent_${userInfo.uuid}_${Date.now()}_test`
    }]

    console.log('📋 生成的事件数据:')
    console.log(JSON.stringify(eventData, null, 2))

    console.log('\n📋 FormData格式 (data字段):')
    console.log(JSON.stringify(eventData))

    console.log('\n🔍 用户数据字段检查:')
    const userData_fields = Object.keys(userData)
    console.log('包含的字段:', userData_fields)
    
    // 检查必需字段
    const requiredFields = ['client_user_agent', 'client_ip_address', 'fbc', 'fbp']
    const missingFields = requiredFields.filter(field => !userData_fields.includes(field))
    
    if (missingFields.length > 0) {
      console.log('❌ 缺少必需字段:', missingFields)
    } else {
      console.log('✅ 所有必需字段都存在')
    }

    // 检查字段顺序（与工作示例对比）
    const workingExampleOrder = ['em', 'ph', 'client_ip_address', 'client_user_agent', 'fbc', 'fbp']
    console.log('\n📊 字段顺序对比:')
    console.log('工作示例顺序:', workingExampleOrder)
    console.log('我们的顺序:', userData_fields)

    // 生成可以直接在控制台测试的代码
    console.log('\n🚀 可以直接在浏览器控制台运行的测试代码:')
    console.log(`
const eventData = ${JSON.stringify(eventData)};
const formData = new FormData();
formData.append('data', JSON.stringify(eventData));

fetch('https://api-test.zhijianyuzhou.com/api/v1/meta-api.forward', {
    method: 'POST',
    body: formData,
    headers: {
        'Authorization': 'Bearer YOUR_TOKEN_HERE'
    }
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
`)

  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 运行测试
testDataFormat()
