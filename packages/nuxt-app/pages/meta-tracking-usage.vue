<!--
  Nuxt App 中 Meta 事件跟踪的使用示例
  展示如何在不同页面和场景中使用 useMetaTracking
-->

<template>
  <div class="meta-tracking-examples">
    <h1>Meta 事件跟踪示例</h1>
    
    <!-- 故事查看示例 -->
    <section class="story-section">
      <h2>故事查看跟踪</h2>
      <div v-if="story" class="story-card">
        <h3>{{ story.title }}</h3>
        <p>{{ story.description }}</p>
        <button @click="viewStory">查看故事</button>
      </div>
    </section>

    <!-- 搜索示例 -->
    <section class="search-section">
      <h2>搜索跟踪</h2>
      <input 
        v-model="searchQuery" 
        @keyup.enter="performSearch"
        placeholder="搜索故事..."
      />
      <button @click="performSearch">搜索</button>
    </section>

    <!-- 购物车示例 -->
    <section class="cart-section">
      <h2>购物车跟踪</h2>
      <div class="product">
        <h3>Premium Story Pack</h3>
        <p>价格: $29.99</p>
        <button @click="addToCart">添加到购物车</button>
        <button @click="initiateCheckout">发起结账</button>
        <button @click="completePurchase">完成购买</button>
      </div>
    </section>

    <!-- 订阅示例 -->
    <section class="subscription-section">
      <h2>订阅跟踪</h2>
      <div class="subscription-plan">
        <h3>月度会员</h3>
        <p>价格: $19.99/月</p>
        <button @click="subscribe">订阅</button>
      </div>
    </section>

    <!-- 队列状态 -->
    <section class="queue-status">
      <h2>队列状态</h2>
      <div class="status-info">
        <p>队列长度: {{ queueStatus.queueLength }}</p>
        <p>正在处理: {{ queueStatus.isProcessing ? '是' : '否' }}</p>
        <p>待重试: {{ queueStatus.pendingRetries }}</p>
        <button @click="refreshQueueStatus">刷新状态</button>
        <button @click="flushQueue">立即发送</button>
        <button @click="clearQueue">清空队列</button>
      </div>
    </section>
  </div>
</template>

<script setup>
// 使用 Meta 跟踪 composable
const {
  trackStoryView,
  trackSearch,
  trackAddToCart,
  trackInitiateCheckout,
  trackPurchase,
  trackSubscribe,
  getQueueStatus,
  flushQueue,
  clearQueue
} = useMetaTracking()

// 响应式数据
const searchQuery = ref('')
const queueStatus = ref(getQueueStatus())

// 模拟故事数据
const story = ref({
  id: 'story-romance-001',
  title: 'Romantic Adventure',
  description: 'A beautiful love story...',
  category: 'romance'
})

// 故事查看事件
const viewStory = async () => {
  try {
    const result = await trackStoryView({
      storyId: story.value.id,
      storyTitle: story.value.title,
      category: story.value.category
    })
    
    if (result.success) {
      console.log('✅ 故事查看事件已上报')
    } else {
      console.error('❌ 故事查看事件上报失败:', result.error)
    }
  } catch (error) {
    console.error('故事查看跟踪出错:', error)
  }
}

// 搜索事件
const performSearch = async () => {
  if (!searchQuery.value.trim()) return
  
  try {
    const result = await trackSearch(searchQuery.value, 'romance')
    
    if (result.success) {
      console.log('✅ 搜索事件已上报:', searchQuery.value)
    } else {
      console.error('❌ 搜索事件上报失败:', result.error)
    }
  } catch (error) {
    console.error('搜索跟踪出错:', error)
  }
}

// 添加到购物车事件
const addToCart = async () => {
  try {
    const result = await trackAddToCart({
      contentId: 'premium-story-pack',
      contentName: 'Premium Story Pack',
      value: 29.99,
      currency: 'usd',
      category: 'digital-content'
    })
    
    if (result.success) {
      console.log('✅ 添加到购物车事件已上报')
    } else {
      console.error('❌ 添加到购物车事件上报失败:', result.error)
    }
  } catch (error) {
    console.error('添加到购物车跟踪出错:', error)
  }
}

// 发起结账事件
const initiateCheckout = async () => {
  try {
    const result = await trackInitiateCheckout({
      value: 29.99,
      currency: 'usd',
      numItems: 1,
      contents: [
        {
          id: 'premium-story-pack',
          quantity: 1,
          item_price: 29.99
        }
      ]
    })
    
    if (result.success) {
      console.log('✅ 发起结账事件已上报')
    } else {
      console.error('❌ 发起结账事件上报失败:', result.error)
    }
  } catch (error) {
    console.error('发起结账跟踪出错:', error)
  }
}

// 完成购买事件
const completePurchase = async () => {
  try {
    const orderId = `order-${Date.now()}`
    const result = await trackPurchase({
      value: 29.99,
      currency: 'usd',
      orderId,
      productId: 'premium-story-pack',
      productName: 'Premium Story Pack',
      quantity: 1
    })
    
    if (result.success) {
      console.log('✅ 购买事件已上报, 订单ID:', orderId)
    } else {
      console.error('❌ 购买事件上报失败:', result.error)
    }
  } catch (error) {
    console.error('购买跟踪出错:', error)
  }
}

// 订阅事件
const subscribe = async () => {
  try {
    const result = await trackSubscribe({
      value: 19.99,
      currency: 'usd',
      subscriptionType: 'Monthly Premium',
      predictedLtv: 239.88 // 预测年度价值
    })
    
    if (result.success) {
      console.log('✅ 订阅事件已上报')
    } else {
      console.error('❌ 订阅事件上报失败:', result.error)
    }
  } catch (error) {
    console.error('订阅跟踪出错:', error)
  }
}

// 刷新队列状态
const refreshQueueStatus = () => {
  queueStatus.value = getQueueStatus()
}

// 页面挂载时的处理
onMounted(() => {
  // 可以在这里添加页面加载时的事件跟踪
  console.log('Meta 跟踪示例页面已加载')
  
  // 定期刷新队列状态
  const interval = setInterval(() => {
    queueStatus.value = getQueueStatus()
  }, 2000)
  
  // 页面卸载时清理定时器
  onUnmounted(() => {
    clearInterval(interval)
  })
})

// 页面元数据
useHead({
  title: 'Meta 事件跟踪示例',
  meta: [
    { name: 'description', content: '展示如何在 Nuxt App 中使用 Meta Conversions API 事件跟踪' }
  ]
})
</script>

<style scoped>
.meta-tracking-examples {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

h1, h2 {
  color: #333;
}

.story-card, .product, .subscription-plan {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
  margin: 10px 0;
}

button {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin: 5px;
}

button:hover {
  background: #0056b3;
}

input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 10px;
  width: 200px;
}

.status-info {
  background: #e9ecef;
  padding: 15px;
  border-radius: 5px;
}

.status-info p {
  margin: 5px 0;
  font-family: monospace;
}
</style>
