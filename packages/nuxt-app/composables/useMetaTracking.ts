/**
 * Meta Conversions API 事件跟踪 Composable
 * 为 Nuxt App 提供 Meta 事件上报功能
 */

import { createMetaReporter, MetaEventName, type MetaReportingService } from 'shared'

export const useMetaTracking = () => {
  const config = useRuntimeConfig()
  const userStore = useUserStore()
  
  // 创建Meta事件上报器
  const metaReporter = createMetaReporter({
    apiBase: config.public.apiBase as string,
    authTokenGetter: () => userStore.token,
    userDataGetter: () => userStore.userInfo,
    debug: config.public.deploymentEnv !== 'production',
    retryConfig: {
      maxRetries: 3,
      retryDelay: 2000,
      backoffMultiplier: 2
    },
    batchConfig: {
      maxBatchSize: 5,
      flushInterval: 3000
    },
    defaultActionSource: 'website'
  })

  /**
   * 跟踪购买事件
   */
  const trackPurchase = async (params: {
    value: number
    currency?: string
    orderId?: string
    productId?: string
    productName?: string
    quantity?: number
  }) => {
    try {
      return await metaReporter.reportEvent(MetaEventName.Purchase, {
        currency: params.currency || 'usd',
        value: params.value,
        order_id: params.orderId,
        contents: params.productId ? [
          {
            id: params.productId,
            quantity: params.quantity || 1,
            item_price: params.value,
            title: params.productName,
            delivery_category: 'home_delivery' as const
          }
        ] : undefined
      })
    } catch (error) {
      console.error('Failed to track purchase:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪注册完成事件
   */
  const trackRegistration = async () => {
    try {
      return await metaReporter.reportEvent(MetaEventName.CompleteRegistration, {
        currency: 'usd',
        value: 0
      })
    } catch (error) {
      console.error('Failed to track registration:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪故事查看事件
   */
  const trackStoryView = async (params: {
    storyId: string
    storyTitle: string
    category?: string
    author?: string
  }) => {
    try {
      return await metaReporter.reportEvent(MetaEventName.ViewContent, {
        content_type: 'story',
        content_ids: [params.storyId],
        content_name: params.storyTitle,
        content_category: params.category
      })
    } catch (error) {
      console.error('Failed to track story view:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪搜索事件
   */
  const trackSearch = async (searchString: string, category?: string) => {
    try {
      return await metaReporter.reportEvent(MetaEventName.Search, {
        search_string: searchString,
        content_category: category
      })
    } catch (error) {
      console.error('Failed to track search:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪添加到购物车事件
   */
  const trackAddToCart = async (params: {
    contentId: string
    contentName: string
    value: number
    currency?: string
    category?: string
  }) => {
    try {
      return await metaReporter.reportEvent(MetaEventName.AddToCart, {
        content_ids: [params.contentId],
        content_name: params.contentName,
        currency: params.currency || 'usd',
        value: params.value,
        content_category: params.category
      })
    } catch (error) {
      console.error('Failed to track add to cart:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪发起结账事件
   */
  const trackInitiateCheckout = async (params: {
    value: number
    currency?: string
    numItems?: number
    contents?: Array<{
      id: string
      quantity: number
      item_price?: number
    }>
  }) => {
    try {
      return await metaReporter.reportEvent(MetaEventName.InitiateCheckout, {
        currency: params.currency || 'usd',
        value: params.value,
        num_items: params.numItems,
        contents: params.contents?.map(item => ({
          id: item.id,
          quantity: item.quantity,
          item_price: item.item_price,
          delivery_category: 'home_delivery' as const
        }))
      })
    } catch (error) {
      console.error('Failed to track initiate checkout:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪订阅事件
   */
  const trackSubscribe = async (params: {
    value: number
    currency?: string
    subscriptionType?: string
    predictedLtv?: number
  }) => {
    try {
      return await metaReporter.reportEvent(MetaEventName.Subscribe, {
        currency: params.currency || 'usd',
        value: params.value,
        content_name: params.subscriptionType,
        predicted_ltv: params.predictedLtv
      })
    } catch (error) {
      console.error('Failed to track subscribe:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪潜在客户事件
   */
  const trackLead = async (params: {
    value?: number
    currency?: string
    contentName?: string
  }) => {
    try {
      return await metaReporter.reportEvent(MetaEventName.Lead, {
        currency: params.currency || 'usd',
        value: params.value || 0,
        content_name: params.contentName
      })
    } catch (error) {
      console.error('Failed to track lead:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 跟踪添加支付信息事件
   */
  const trackAddPaymentInfo = async (params: {
    value?: number
    currency?: string
    contentCategory?: string
  }) => {
    try {
      return await metaReporter.reportEvent(MetaEventName.AddPaymentInfo, {
        currency: params.currency || 'usd',
        value: params.value || 0,
        content_category: params.contentCategory
      })
    } catch (error) {
      console.error('Failed to track add payment info:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 批量跟踪事件
   */
  const trackEvents = async (
    events: Array<{
      eventName: MetaEventName | string
      customData?: any
      options?: {
        actionSource?: 'website' | 'app' | 'email'
        eventSourceUrl?: string
        eventId?: string
      }
    }>
  ) => {
    try {
      return await metaReporter.reportEvents(events)
    } catch (error) {
      console.error('Failed to track batch events:', error)
      return events.map(() => ({ success: false, error: error.message }))
    }
  }

  /**
   * 获取队列状态
   */
  const getQueueStatus = () => {
    return metaReporter.getQueueStatus()
  }

  /**
   * 立即刷新队列
   */
  const flushQueue = async () => {
    try {
      await metaReporter.flushQueue()
    } catch (error) {
      console.error('Failed to flush queue:', error)
    }
  }

  /**
   * 清空队列
   */
  const clearQueue = () => {
    metaReporter.clearQueue()
  }

  return {
    // 核心上报器
    metaReporter,
    
    // 事件跟踪方法
    trackPurchase,
    trackRegistration,
    trackStoryView,
    trackSearch,
    trackAddToCart,
    trackInitiateCheckout,
    trackSubscribe,
    trackLead,
    trackAddPaymentInfo,
    trackEvents,
    
    // 队列管理
    getQueueStatus,
    flushQueue,
    clearQueue
  }
}
